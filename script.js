// Global variables
let uploadedData = [];
let filteredData = [];

// DOM elements
const batchCreateBtn = document.getElementById('batchCreateBtn');
const batchModal = document.getElementById('batchModal');
const closeModal = document.querySelector('.close');
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const selectFileBtn = document.getElementById('selectFileBtn');
const downloadTemplate = document.getElementById('downloadTemplate');
const tableContainer = document.getElementById('tableContainer');
const tableBody = document.getElementById('tableBody');
const summary = document.getElementById('summary');
const tabButtons = document.querySelectorAll('.tab-button');
const tabContainer = document.getElementById('tabContainer');

// Event listeners
batchCreateBtn.addEventListener('click', () => {
    batchModal.style.display = 'block';
});

closeModal.addEventListener('click', () => {
    batchModal.style.display = 'none';
});

window.addEventListener('click', (event) => {
    if (event.target === batchModal) {
        batchModal.style.display = 'none';
    }
});

selectFileBtn.addEventListener('click', () => {
    fileInput.click();
});

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.style.borderColor = '#4CAF50';
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.style.borderColor = '#ccc';
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.style.borderColor = '#ccc';
    
    if (e.dataTransfer.files.length) {
        handleFile(e.dataTransfer.files[0]);
    }
});

fileInput.addEventListener('change', (e) => {
    if (e.target.files.length) {
        handleFile(e.target.files[0]);
    }
});

downloadTemplate.addEventListener('click', downloadExcelTemplate);

tabButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Remove active class from all buttons
        tabButtons.forEach(btn => btn.classList.remove('active'));
        // Add active class to clicked button
        button.classList.add('active');
        // Filter data based on selected tab
        filterData(button.dataset.tab);
    });
});

// Handle file upload
function handleFile(file) {
    const reader = new FileReader();
    
    reader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get the first worksheet
        const worksheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[worksheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        // Process and validate data
        processData(jsonData);
    };
    
    reader.readAsArrayBuffer(file);
}

// Process and validate uploaded data
function processData(data) {
    uploadedData = [];
    
    data.forEach((item, index) => {
        // Validate each item
        const validation = validateItem(item);
        
        // Create processed item
        const processedItem = {
            id: index,
            softwareName: item['软件名称'] || '',
            softwareType: item['软件类别'] || '',
            shortName: item['软件简称'] || '',
            version: item['版本号'] || '',
            copyrightHolders: item['著作权人'] || '',
            agentAccount: item['代理人账户号'] || '',
            isValid: validation.isValid,
            errors: validation.errors,
            submitType: item['代理人账户号'] ? '代理提交' : '申请人提交'
        };
        
        uploadedData.push(processedItem);
    });
    
    // Display data
    displayData(uploadedData);
    
    // Show summary
    showSummary(uploadedData);
    
    // Show tabs
    tabContainer.style.display = 'block';
}

// Validate individual item
function validateItem(item) {
    const errors = [];
    let isValid = true;
    
    // Validation 1: Software name must be Chinese or English, no special symbols
    if (item['软件名称']) {
        const nameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/;
        if (!nameRegex.test(item['软件名称'])) {
            isValid = false;
            errors.push({ code: 1, message: '软件名称必须是简体中文或英文，不得出现特殊符号或其他语言' });
        }
    }
    
    // Validation 2: Software name must end with "软件", "系统", or "平台"
    if (item['软件名称']) {
        if (!item['软件名称'].endsWith('软件') && 
            !item['软件名称'].endsWith('系统') && 
            !item['软件名称'].endsWith('平台')) {
            isValid = false;
            errors.push({ code: 2, message: '软件名称的结尾必须是以"软件"/"系统"/"平台"结尾' });
        }
    }
    
    // Validation 3: Short name must not be longer than software name
    if (item['软件简称'] && item['软件名称']) {
        if (item['软件简称'].length > item['软件名称'].length) {
            isValid = false;
            errors.push({ code: 3, message: '软件简称不得比软件名称长' });
        }
    }
    
    // Validation 4: Version format must be X.Y or VX.Y (V must be uppercase)
    if (item['版本号']) {
        const versionRegex = /^(\d+\.\d+|V\d+\.\d+)$/;
        if (!versionRegex.test(item['版本号'])) {
            isValid = false;
            errors.push({ code: 4, message: '版本号格式必须是X.Y 或VX.Y，V必须是大写' });
        }
    }
    
    // We'll check for duplicate names later when we have all data
    
    return { isValid, errors };
}

// Check for duplicate software names for the same copyright holder
function checkForDuplicates(data) {
    const copyrightMap = new Map();
    
    data.forEach(item => {
        if (item.copyrightHolders) {
            const holders = item.copyrightHolders.split('&');
            holders.forEach(holder => {
                const key = `${holder.trim()}-${item.softwareName}`;
                if (copyrightMap.has(key)) {
                    // Found duplicate
                    item.isValid = false;
                    item.errors = item.errors || [];
                    item.errors.push({
                        code: 5,
                        message: `著作权人"${holder.trim()}"的软件名称"${item.softwareName}"重复`
                    });
                } else {
                    copyrightMap.set(key, true);
                }
            });
        }
    });
    
    return data;
}

// Display data in table
function displayData(data) {
    // Check for duplicates
    const dataWithDuplicates = checkForDuplicates([...data]);
    
    // Set filtered data to all data initially
    filteredData = dataWithDuplicates;
    
    // Clear table
    tableBody.innerHTML = '';
    
    // Populate table
    filteredData.forEach(item => {
        const row = document.createElement('tr');
        if (!item.isValid) {
            row.classList.add('error-row');
        }
        
        // Add unauthorized tag randomly to copyright holders or agent account
        let copyrightHoldersDisplay = item.copyrightHolders;
        let agentAccountDisplay = item.agentAccount;
        
        // Randomly add unauthorized tag
        if (Math.random() > 0.7) {
            copyrightHoldersDisplay = `
                <div>
                    ${item.copyrightHolders}
                    <span class="unauthorized-tag">
                        未授权
                        <span class="tooltip">可在提交过程中完成授权</span>
                    </span>
                </div>
            `;
        }
        
        if (item.agentAccount && Math.random() > 0.7) {
            agentAccountDisplay = `
                <div>
                    ${item.agentAccount}
                    <span class="unauthorized-tag">
                        未授权
                        <span class="tooltip">可在提交过程中完成授权</span>
                    </span>
                </div>
            `;
        }
        
        row.innerHTML = `
            <td>${item.softwareName}</td>
            <td>${item.softwareType}</td>
            <td>${item.shortName}</td>
            <td>${item.version}</td>
            <td>${copyrightHoldersDisplay}</td>
            <td>${item.submitType}</td>
            <td>${agentAccountDisplay}</td>
            <td>${item.errors ? item.errors.map(e => e.message).join('; ') : ''}</td>
            <td>
                ${!item.isValid ? `<button class="edit-btn" onclick="editItem(${item.id})">修改</button>` : ''}
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    // Show table
    tableContainer.style.display = 'block';
}

// Filter data based on tab selection
function filterData(tab) {
    if (tab === 'all') {
        filteredData = uploadedData;
    } else if (tab === 'problem') {
        filteredData = uploadedData.filter(item => !item.isValid);
    }
    
    displayData(filteredData);
}

// Show summary of upload
function showSummary(data) {
    const validCount = data.filter(item => item.isValid).length;
    const invalidCount = data.length - validCount;
    
    document.getElementById('successCount').textContent = validCount;
    document.getElementById('failCount').textContent = invalidCount;
    document.getElementById('problemCount').textContent = invalidCount;
    
    summary.style.display = 'block';
}

// Download Excel template
function downloadExcelTemplate() {
    // Create worksheet data
    const data = [
        ['软件名称', '软件类别', '软件简称', '版本号', '著作权人', '代理人账户号'],
        ['', 'PC软件/安卓应用/iOS应用/小程序/嵌入式', '', 'X.Y 或 VX.Y', '多个著作权人用&分割', '']
    ];
    
    // Create workbook
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '软著批量创建模板');
    
    // Download file
    XLSX.writeFile(wb, '软著批量创建模板.xlsx');
}

// Edit item function (to be implemented)
function editItem(id) {
    alert(`编辑功能待实现，ID: ${id}`);
    // In a full implementation, this would open an edit modal
    // with the item's current data for modification
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    // Any initialization code would go here
});