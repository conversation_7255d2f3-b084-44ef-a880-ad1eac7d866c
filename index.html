<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量创建软著</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background-color: #45a049;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 5px;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
        
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        .upload-area:hover {
            border-color: #4CAF50;
        }
        
        .template-link {
            color: #4CAF50;
            text-decoration: underline;
            cursor: pointer;
            display: block;
            margin: 10px 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .data-table th {
            background-color: #f2f2f2;
        }
        
        .error-row {
            background-color: #ffebee;
        }
        
        .tab-container {
            margin: 20px 0;
        }
        
        .tab-button {
            background-color: #f1f1f1;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        
        .tab-button.active {
            background-color: #4CAF50;
            color: white;
        }
        
        .unauthorized-tag {
            background-color: #ffeb3b;
            color: #000;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 12px;
            position: relative;
            display: inline-block;
        }
        
        .tooltip {
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 1;
        }
        
        .unauthorized-tag:hover .tooltip {
            opacity: 1;
        }
        
        .edit-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .summary {
            margin: 20px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>软著批量创建系统</h1>
    <button id="batchCreateBtn" class="btn">批量创建</button>

    <!-- 批量创建弹窗 -->
    <div id="batchModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>批量创建</h2>
            
            <div class="upload-area" id="uploadArea">
                <p>点击或拖拽文件到此区域上传</p>
                <p>支持 xls, xlsx 格式</p>
                <input type="file" id="fileInput" accept=".xls,.xlsx" style="display: none;">
                <button class="btn" id="selectFileBtn">选择文件</button>
            </div>
            
            <div class="template-link" id="downloadTemplate">
                下载模板文件（请务必使用最新模板）
            </div>
            
            <div class="summary" id="summary" style="display: none;">
                <p>成功导入 <span id="successCount">0</span> 件，失败 <span id="failCount">0</span> 件，<span id="problemCount">0</span> 件存在问题</p>
            </div>
            
            <div class="tab-container" id="tabContainer" style="display: none;">
                <button class="tab-button active" data-tab="all">全部</button>
                <button class="tab-button" data-tab="problem">存在问题</button>
            </div>
            
            <div id="tableContainer" style="display: none;">
                <table class="data-table" id="dataTable">
                    <thead>
                        <tr>
                            <th>软件名称</th>
                            <th>软件类别</th>
                            <th>软件简称</th>
                            <th>版本号</th>
                            <th>著作权人</th>
                            <th>提交方式</th>
                            <th>代理人账户号</th>
                            <th>说明</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="script.js"></script>
</body>
</html>