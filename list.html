<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水质指标管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .indicator-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .water-type-tag {
            transition: all 0.2s ease;
        }

        .water-type-tag:hover {
            transform: scale(1.05);
        }

        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .fade-enter-active,
        .fade-leave-active {
            transition: opacity 0.3s;
        }

        .fade-enter,
        .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>

<body class="bg-gray-50">
    <div class="haha p-6 max-w-7xl mx-auto">
        <!-- 页面标题和操作区 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">水质指标管理</h1>
                <p class="text-gray-600 mt-2">自定义和管理水质监测指标及其标准值</p>
            </div>
            <button id="addIndicatorBtn"
                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center transition-colors">
                <i class="fas fa-plus mr-2"></i> 新增指标
            </button>
        </div>

        <!-- 指标分类和搜索区 -->
        <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex space-x-2">
                    <button
                        class="water-type-filter px-4 py-2 rounded-full bg-blue-100 text-blue-700 font-medium water-type-tag"
                        data-type="all">
                        全部指标
                    </button>
                    <button
                        class="water-type-filter px-4 py-2 rounded-full bg-green-100 text-green-700 font-medium water-type-tag"
                        data-type="地表水">
                        地表水
                    </button>
                    <button
                        class="water-type-filter px-4 py-2 rounded-full bg-purple-100 text-purple-700 font-medium water-type-tag"
                        data-type="地下水">
                        地下水
                    </button>
                    <button
                        class="water-type-filter px-4 py-2 rounded-full bg-amber-100 text-amber-700 font-medium water-type-tag"
                        data-type="饮用水">
                        饮用水
                    </button>
                </div>

                <div class="relative flex-1 max-w-md">
                    <input type="text" id="indicatorSearch" placeholder="搜索指标名称或描述..."
                        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 指标卡片展示区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- 指标卡片将通过JS动态生成 -->
        </div>

        <!-- 指标统计图表 -->
        <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">水质指标分布统计</h3>
            <div class="flex flex-wrap items-center justify-between gap-6">
                <div class="flex-1 min-w-0">
                    <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                        <p class="text-gray-500">此处为指标类型分布饼图</p>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                        <p class="text-gray-500">此处为指标标准值范围柱状图</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增/编辑指标模态框 -->
        <div id="indicatorModal"
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto custom-scrollbar">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold text-gray-800" id="modalTitle">新增水质指标</h3>
                        <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form id="indicatorForm" class="space-y-4">
                        <input type="hidden" id="dataId" name="dataId">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="indicatorName" class="block text-sm font-medium text-gray-700 mb-1">指标名称
                                    *</label>
                                <input type="text" id="indicatorName" name="indicatorName" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            </div>

                            <div>
                                <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">单位 *</label>
                                <input type="text" id="unit" name="unit" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            </div>
                        </div>

                        <div>
                            <label for="standardValue" class="block text-sm font-medium text-gray-700 mb-1">标准值
                                *</label>
                            <div class="flex items-center space-x-2">
                                <input type="text" id="standardValue" name="standardValue" required
                                    class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                                <button type="button" id="addRangeBtn"
                                    class="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                                    <i class="fas fa-sliders mr-1"></i> 范围设置
                                </button>
                            </div>
                            <div id="rangeSettings" class="mt-2 hidden">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">最小值</label>
                                        <input type="number" id="minValue"
                                            class="w-full px-3 py-1 border border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">最大值</label>
                                        <input type="number" id="maxValue"
                                            class="w-full px-3 py-1 border border-gray-300 rounded-md">
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <label class="block text-xs text-gray-500 mb-1">警告阈值</label>
                                    <input type="number" id="warningValue"
                                        class="w-full px-3 py-1 border border-gray-300 rounded-md">
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">水体类型 *</label>
                            <div class="flex flex-wrap gap-2">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="waterType" value="地表水" class="h-4 w-4 text-blue-600"
                                        checked>
                                    <span class="ml-2 text-gray-700">地表水</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="waterType" value="地下水" class="h-4 w-4 text-blue-600">
                                    <span class="ml-2 text-gray-700">地下水</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="waterType" value="饮用水" class="h-4 w-4 text-blue-600">
                                    <span class="ml-2 text-gray-700">饮用水</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">监测目的</label>
                            <div class="flex flex-wrap gap-2">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="monitoringPurpose" value="环境监测"
                                        class="h-4 w-4 text-blue-600">
                                    <span class="ml-2 text-gray-700">环境监测</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="monitoringPurpose" value="水质评估"
                                        class="h-4 w-4 text-blue-600">
                                    <span class="ml-2 text-gray-700">水质评估</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="monitoringPurpose" value="科研"
                                        class="h-4 w-4 text-blue-600">
                                    <span class="ml-2 text-gray-700">科研</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">指标描述</label>
                            <textarea id="description" name="description" rows="3"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"></textarea>
                        </div>

                        <div>
                            <label for="indicatorImage"
                                class="block text-sm font-medium text-gray-700 mb-1">指标图片</label>
                            <div class="flex items-center space-x-4">
                                <div class="w-24 h-24 bg-gray-100 rounded-lg border border-dashed border-gray-300 flex items-center justify-center overflow-hidden"
                                    id="imagePreview">
                                    <i class="fas fa-image text-gray-400 text-2xl"></i>
                                </div>
                                <div class="flex-1">
                                    <input type="file" id="indicatorImage" name="indicatorImage" accept="image/*"
                                        class="hidden">
                                    <button type="button" id="uploadImageBtn"
                                        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                                        <i class="fas fa-upload mr-2"></i>上传图片
                                    </button>
                                    <p class="text-xs text-gray-500 mt-1">支持JPG, PNG格式，大小不超过2MB</p>
                                </div>
                            </div>
                        </div>

                        <div class="pt-4 flex justify-end space-x-3">
                            <button type="button" id="cancelBtn"
                                class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">取消</button>
                            <button type="submit"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">保存指标</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const indicators = [
            {
                dataId: '1',
                indicatorName: 'pH值',
                unit: '无量纲',
                standardValue: '6.5-8.5',
                waterType: '地表水',
                monitoringPurpose: ['环境监测', '水质评估'],
                lastUpdated: '2023-05-15',
                description: '表示水体的酸碱度，是水质基本指标之一',
                indicatorImage: null
            },
            {
                dataId: '2',
                indicatorName: '溶解氧(DO)',
                unit: 'mg/L',
                standardValue: '≥5',
                waterType: '地表水',
                monitoringPurpose: ['环境监测', '水质评估', '科研'],
                lastUpdated: '2023-06-20',
                description: '水中溶解的氧气含量，反映水体自净能力',
                indicatorImage: null
            },
            {
                dataId: '3',
                indicatorName: '氨氮',
                unit: 'mg/L',
                standardValue: '≤0.5',
                waterType: '地下水',
                monitoringPurpose: ['环境监测'],
                lastUpdated: '2023-04-10',
                description: '反映水体受有机物污染程度的重要指标',
                indicatorImage: null
            },
            {
                dataId: '4',
                indicatorName: '总大肠菌群',
                unit: '个/L',
                standardValue: '≤100',
                waterType: '饮用水',
                monitoringPurpose: ['水质评估'],
                lastUpdated: '2023-07-05',
                description: '指示水体受粪便污染程度的重要微生物指标',
                indicatorImage: null
            },
            {
                dataId: '5',
                indicatorName: '高锰酸盐指数',
                unit: 'mg/L',
                standardValue: '≤4',
                waterType: '地表水',
                monitoringPurpose: ['环境监测', '科研'],
                lastUpdated: '2023-03-18',
                description: '反映水体中有机物含量的综合指标',
                indicatorImage: null
            },
            {
                dataId: '6',
                indicatorName: '氟化物',
                unit: 'mg/L',
                standardValue: '≤1.0',
                waterType: '地下水',
                monitoringPurpose: ['水质评估'],
                lastUpdated: '2023-08-22',
                description: '地下水中常见的无机污染物，过量对人体有害',
                indicatorImage: null
            }
        ];

        // DOM元素
        const indicatorCardsContainer = document.querySelector('.grid');
        const addIndicatorBtn = document.getElementById('addIndicatorBtn');
        const indicatorModal = document.getElementById('indicatorModal');
        const closeModal = document.getElementById('closeModal');
        const cancelBtn = document.getElementById('cancelBtn');
        const indicatorForm = document.getElementById('indicatorForm');
        const modalTitle = document.getElementById('modalTitle');
        const waterTypeFilters = document.querySelectorAll('.water-type-filter');
        const indicatorSearch = document.getElementById('indicatorSearch');
        const addRangeBtn = document.getElementById('addRangeBtn');
        const rangeSettings = document.getElementById('rangeSettings');
        const uploadImageBtn = document.getElementById('uploadImageBtn');
        const indicatorImage = document.getElementById('indicatorImage');
        const imagePreview = document.getElementById('imagePreview');

        // 渲染指标卡片
        function renderIndicatorCards(filteredIndicators = indicators) {
            indicatorCardsContainer.innerHTML = '';

            filteredIndicators.forEach(indicator => {
                const card = document.createElement('div');
                card.className = 'bg-white rounded-xl shadow-sm p-5 indicator-card transition-all hover:cursor-pointer';

                // 根据水体类型设置卡片边框颜色
                let borderColor = 'border-blue-200';
                if (indicator.waterType === '地下水') borderColor = 'border-purple-200';
                if (indicator.waterType === '饮用水') borderColor = 'border-amber-200';

                card.innerHTML = `
                    <div class="border-l-4 ${borderColor} pl-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">${indicator.indicatorName}</h3>
                            <span class="text-sm px-3 py-1 rounded-full ${getWaterTypeBadgeClass(indicator.waterType)}">
                                ${indicator.waterType}
                            </span>
                        </div>
                        <div class="flex items-center text-gray-600 mb-3">
                            <span class="mr-4">标准值: <span class="font-medium">${indicator.standardValue}</span></span>
                            <span>单位: <span class="font-medium">${indicator.unit}</span></span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-gray-600 text-sm line-clamp-2">${indicator.description || '暂无描述'}</p>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mb-4">
                        ${indicator.monitoringPurpose.map(purpose => `
                            <span class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded">${purpose}</span>
                        `).join('')}
                    </div>
                    
                    <div class="flex justify-between items-center text-sm text-gray-500">
                        <span>最后更新: ${indicator.lastUpdated}</span>
                        <div class="flex space-x-2">
                            <button class="edit-btn px-3 py-1 text-blue-600 hover:bg-blue-50 rounded-lg" data-id="${indicator.dataId}">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="delete-btn px-3 py-1 text-red-600 hover:bg-red-50 rounded-lg" data-id="${indicator.dataId}">
                                <i class="fas fa-trash-alt mr-1"></i>删除
                            </button>
                        </div>
                    </div>
                `;

                indicatorCardsContainer.appendChild(card);
            });

            // 添加编辑和删除按钮事件
            document.querySelectorAll('.edit-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const id = btn.getAttribute('data-id');
                    editIndicator(id);
                });
            });

            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const id = btn.getAttribute('data-id');
                    deleteIndicator(id);
                });
            });

            // 添加卡片点击事件
            document.querySelectorAll('.indicator-card').forEach(card => {
                card.addEventListener('click', () => {
                    const id = card.querySelector('.edit-btn').getAttribute('data-id');
                    viewIndicatorDetails(id);
                });
            });
        }

        // 根据水体类型获取徽章类
        function getWaterTypeBadgeClass(waterType) {
            switch (waterType) {
                case '地表水': return 'bg-blue-100 text-blue-700';
                case '地下水': return 'bg-purple-100 text-purple-700';
                case '饮用水': return 'bg-amber-100 text-amber-700';
                default: return 'bg-gray-100 text-gray-700';
            }
        }

        // 查看指标详情
        function viewIndicatorDetails(id) {
            const indicator = indicators.find(i => i.dataId === id);
            if (!indicator) return;

            alert(`查看指标详情:\n名称: ${indicator.indicatorName}\n标准值: ${indicator.standardValue} ${indicator.unit}\n水体类型: ${indicator.waterType}\n描述: ${indicator.description}`);
        }

        // 编辑指标
        function editIndicator(id) {
            const indicator = indicators.find(i => i.dataId === id);
            if (!indicator) return;

            modalTitle.textContent = '编辑水质指标';
            document.getElementById('dataId').value = indicator.dataId;
            document.getElementById('indicatorName').value = indicator.indicatorName;
            document.getElementById('unit').value = indicator.unit;
            document.getElementById('standardValue').value = indicator.standardValue;

            // 设置水体类型单选按钮
            document.querySelector(`input[name="waterType"][value="${indicator.waterType}"]`).checked = true;

            // 设置监测目的复选框
            document.querySelectorAll('input[name="monitoringPurpose"]').forEach(checkbox => {
                checkbox.checked = indicator.monitoringPurpose.includes(checkbox.value);
            });

            document.getElementById('description').value = indicator.description;

            // 如果有图片，显示预览
            if (indicator.indicatorImage) {
                imagePreview.innerHTML = `<img src="${indicator.indicatorImage}" alt="指标图片" class="w-full h-full object-cover">`;
            } else {
                imagePreview.innerHTML = '<i class="fas fa-image text-gray-400 text-2xl"></i>';
            }

            openModal();
        }

        // 删除指标
        function deleteIndicator(id) {
            if (confirm('确定要删除这个水质指标吗？此操作不可恢复。')) {
                const index = indicators.findIndex(i => i.dataId === id);
                if (index !== -1) {
                    indicators.splice(index, 1);
                    renderIndicatorCards();
                    alert('指标已删除');
                }
            }
        }

        // 打开模态框
        function openModal() {
            indicatorModal.classList.remove('hidden');
        }

        // 关闭模态框
        function closeModalFunc() {
            indicatorModal.classList.add('hidden');
            indicatorForm.reset();
            imagePreview.innerHTML = '<i class="fas fa-image text-gray-400 text-2xl"></i>';
        }

        // 初始化
        function init() {
            renderIndicatorCards();

            // 事件监听
            addIndicatorBtn.addEventListener('click', () => {
                modalTitle.textContent = '新增水质指标';
                document.getElementById('dataId').value = '';
                indicatorForm.reset();
                openModal();
            });

            closeModal.addEventListener('click', closeModalFunc);
            cancelBtn.addEventListener('click', closeModalFunc);

            // 表单提交
            indicatorForm.addEventListener('submit', (e) => {
                e.preventDefault();

                const formData = new FormData(indicatorForm);
                const data = {
                    dataId: formData.get('dataId'),
                    indicatorName: formData.get('indicatorName'),
                    unit: formData.get('unit'),
                    standardValue: formData.get('standardValue'),
                    waterType: formData.get('waterType'),
                    monitoringPurpose: Array.from(formData.getAll('monitoringPurpose')),
                    description: formData.get('description'),
                    lastUpdated: new Date().toISOString().split('T')[0],
                    indicatorImage: null
                };

                if (data.dataId) {
                    // 更新现有指标
                    const index = indicators.findIndex(i => i.dataId === data.dataId);
                    if (index !== -1) {
                        indicators[index] = { ...indicators[index], ...data };
                    }
                } else {
                    // 新增指标
                    data.dataId = (indicators.length + 1).toString();
                    indicators.push(data);
                }

                renderIndicatorCards();
                closeModalFunc();
                alert('指标已保存');
            });

            // 水体类型筛选
            waterTypeFilters.forEach(filter => {
                filter.addEventListener('click', () => {
                    const type = filter.getAttribute('data-type');
                    if (type === 'all') {
                        renderIndicatorCards();
                    } else {
                        const filtered = indicators.filter(i => i.waterType === type);
                        renderIndicatorCards(filtered);
                    }
                });
            });

            // 搜索功能
            indicatorSearch.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                if (!searchTerm) {
                    renderIndicatorCards();
                    return;
                }

                const filtered = indicators.filter(i =>
                    i.indicatorName.toLowerCase().includes(searchTerm) ||
                    (i.description && i.description.toLowerCase().includes(searchTerm))
                );
                renderIndicatorCards(filtered);
            });

            // 范围设置切换
            addRangeBtn.addEventListener('click', () => {
                rangeSettings.classList.toggle('hidden');
            });

            // 图片上传
            uploadImageBtn.addEventListener('click', () => {
                indicatorImage.click();
            });

            indicatorImage.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        imagePreview.innerHTML = `<img src="${event.target.result}" alt="预览" class="w-full h-full object-cover">`;
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>

</html>