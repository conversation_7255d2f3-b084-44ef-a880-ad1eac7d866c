<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检测设备管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
        }

        .status-online {
            background-color: #10B981;
            box-shadow: 0 0 6px #10B981;
        }

        .status-offline {
            background-color: #EF4444;
        }

        .status-warning {
            background-color: #F59E0B;
            box-shadow: 0 0 6px #F59E0B;
        }

        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .connection-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <div class="haha p-6 max-w-7xl mx-auto">
        <!-- 页面标题和操作区 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">检测设备管理</h1>
                <p class="text-gray-600 mt-2">监控和管理水质检测设备及其运行状态</p>
            </div>
            <div class="flex space-x-3">
                <button id="addDeviceBtn"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-plus mr-2"></i> 添加设备
                </button>
                <button id="refreshDevicesBtn"
                    class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 设备概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm p-5">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 text-sm">总设备数</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1">24</h3>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-lg text-blue-600">
                        <i class="fas fa-microscope text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-100">
                    <p class="text-sm text-gray-500 flex items-center">
                        <span class="status-indicator status-online"></span>
                        <span>在线: <span class="font-medium text-gray-700">18</span></span>
                    </p>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm p-5">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 text-sm">今日检测次数</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1">156</h3>
                    </div>
                    <div class="p-3 bg-green-100 rounded-lg text-green-600">
                        <i class="fas fa-vial text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-100">
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                        较昨日 <span class="font-medium text-green-500">+12%</span>
                    </p>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm p-5">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 text-sm">异常设备</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1">3</h3>
                    </div>
                    <div class="p-3 bg-amber-100 rounded-lg text-amber-600">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-100">
                    <p class="text-sm text-gray-500 flex items-center">
                        <span class="status-indicator status-warning"></span>
                        <span>需维护: <span class="font-medium text-gray-700">2</span></span>
                    </p>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm p-5">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 text-sm">设备类型</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1">8</h3>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-lg text-purple-600">
                        <i class="fas fa-tags text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-100">
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-info-circle text-purple-500 mr-1"></i>
                        多种检测设备
                    </p>
                </div>
            </div>
        </div>

        <!-- 设备管理和状态监控区 -->
        <div class="flex flex-col lg:flex-row gap-6 mb-8">
            <!-- 设备列表 -->
            <div class="flex-1 bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="p-5 border-b border-gray-100">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">设备列表</h3>
                        <div class="relative">
                            <input type="text" id="deviceSearch" placeholder="搜索设备..."
                                class="w-48 pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                </div>
                <div class="overflow-y-auto" style="max-height: 500px;">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    设备名称</th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    类型</th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    状态</th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    最后活动</th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="deviceTableBody">
                            <!-- 设备行将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 设备状态监控 -->
            <div class="lg:w-96 bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="p-5 border-b border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-800">设备状态监控</h3>
                </div>
                <div class="p-5">
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">设备连接状态</h4>
                        <div class="h-40 bg-gray-50 rounded-lg flex items-center justify-center">
                            <div class="text-center">
                                <div class="relative inline-block mb-3">
                                    <div
                                        class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-blue-500">
                                        <i class="fas fa-plug text-2xl"></i>
                                    </div>
                                    <span class="status-indicator status-online absolute top-0 right-0"></span>
                                </div>
                                <p class="text-sm font-medium text-gray-700">设备连接正常</p>
                                <p class="text-xs text-gray-500 mt-1">最后检测: 2分钟前</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">实时数据流</h4>
                        <div class="h-40 bg-gray-50 rounded-lg flex items-center justify-center">
                            <div class="text-center">
                                <div class="relative inline-block mb-3 connection-animation">
                                    <div
                                        class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center text-green-500">
                                        <i class="fas fa-wave-square text-2xl"></i>
                                    </div>
                                </div>
                                <p class="text-sm font-medium text-gray-700">接收数据中...</p>
                                <p class="text-xs text-gray-500 mt-1">速率: 12.5 KB/s</p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">设备告警</h4>
                        <div class="space-y-3">
                            <div class="flex items-start p-3 bg-red-50 rounded-lg">
                                <div class="mr-3 mt-1 text-red-500">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">pH传感器异常</p>
                                    <p class="text-xs text-gray-500 mt-1">设备ID: DEV-2023-045</p>
                                    <p class="text-xs text-red-500 mt-1">检测值超出范围</p>
                                </div>
                            </div>

                            <div class="flex items-start p-3 bg-amber-50 rounded-lg">
                                <div class="mr-3 mt-1 text-amber-500">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">需校准设备</p>
                                    <p class="text-xs text-gray-500 mt-1">设备ID: DEV-2023-128</p>
                                    <p class="text-xs text-amber-500 mt-1">已超过校准周期</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备配置和操作区 -->
        <div class="bg-white rounded-xl shadow-sm p-5 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-800">设备配置工具</h3>
                <div class="flex space-x-3">
                    <button
                        class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-download mr-2"></i> 导出配置
                    </button>
                    <button
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center transition-colors">
                        <i class="fas fa-cog mr-2"></i> 批量配置
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <div class="mr-3 p-2 bg-blue-100 rounded-lg text-blue-600">
                            <i class="fas fa-wifi"></i>
                        </div>
                        <h4 class="font-medium text-gray-800">网络配置</h4>
                    </div>
                    <p class="text-sm text-gray-500 mb-4">配置设备的网络连接参数和通信协议</p>
                    <button
                        class="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors">
                        配置网络
                    </button>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <div class="mr-3 p-2 bg-green-100 rounded-lg text-green-600">
                            <i class="fas fa-sliders-h"></i>
                        </div>
                        <h4 class="font-medium text-gray-800">传感器校准</h4>
                    </div>
                    <p class="text-sm text-gray-500 mb-4">校准各类传感器的精度和测量范围</p>
                    <button
                        class="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors">
                        开始校准
                    </button>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <div class="mr-3 p-2 bg-purple-100 rounded-lg text-purple-600">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h4 class="font-medium text-gray-800">采样计划</h4>
                    </div>
                    <p class="text-sm text-gray-500 mb-4">设置设备的自动采样频率和时间计划</p>
                    <button
                        class="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors">
                        设置计划
                    </button>
                </div>
            </div>
        </div>

        <!-- 新增设备模态框 -->
        <div id="deviceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto custom-scrollbar">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold text-gray-800" id="modalDeviceTitle">添加新设备</h3>
                        <button id="closeDeviceModal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form id="deviceForm" class="space-y-4">
                        <input type="hidden" id="deviceId" name="deviceId">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="deviceName" class="block text-sm font-medium text-gray-700 mb-1">设备名称
                                    *</label>
                                <input type="text" id="deviceName" name="deviceName" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            </div>

                            <div>
                                <label for="deviceType" class="block text-sm font-medium text-gray-700 mb-1">设备类型
                                    *</label>
                                <select id="deviceType" name="deviceType" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                                    <option value="">选择设备类型</option>
                                    <option value="pH计">pH计</option>
                                    <option value="溶解氧仪">溶解氧仪</option>
                                    <option value="浊度计">浊度计</option>
                                    <option value="电导率仪">电导率仪</option>
                                    <option value="多参数水质检测仪">多参数水质检测仪</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="deviceModel"
                                    class="block text-sm font-medium text-gray-700 mb-1">设备型号</label>
                                <input type="text" id="deviceModel" name="deviceModel"
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            </div>

                            <div>
                                <label for="serialNumber"
                                    class="block text-sm font-medium text-gray-700 mb-1">序列号</label>
                                <input type="text" id="serialNumber" name="serialNumber"
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            </div>
                        </div>

                        <div>
                            <label for="deviceLocation"
                                class="block text-sm font-medium text-gray-700 mb-1">安装位置</label>
                            <input type="text" id="deviceLocation" name="deviceLocation"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="purchaseDate"
                                    class="block text-sm font-medium text-gray-700 mb-1">购买日期</label>
                                <input type="date" id="purchaseDate" name="purchaseDate"
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            </div>

                            <div>
                                <label for="warrantyExpiry"
                                    class="block text-sm font-medium text-gray-700 mb-1">保修到期</label>
                                <input type="date" id="warrantyExpiry" name="warrantyExpiry"
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                            </div>
                        </div>

                        <div>
                            <label for="deviceDescription"
                                class="block text-sm font-medium text-gray-700 mb-1">设备描述</label>
                            <textarea id="deviceDescription" name="deviceDescription" rows="3"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备状态</label>
                            <div class="flex flex-wrap gap-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="deviceStatus" value="active" class="h-4 w-4 text-blue-600"
                                        checked>
                                    <span class="ml-2 text-gray-700">活跃</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="deviceStatus" value="maintenance"
                                        class="h-4 w-4 text-blue-600">
                                    <span class="ml-2 text-gray-700">维护中</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="deviceStatus" value="inactive"
                                        class="h-4 w-4 text-blue-600">
                                    <span class="ml-2 text-gray-700">停用</span>
                                </label>
                            </div>
                        </div>

                        <div class="pt-4 flex justify-end space-x-3">
                            <button type="button" id="cancelDeviceBtn"
                                class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">取消</button>
                            <button type="submit"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">保存设备</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const devices = [
            {
                deviceId: '1',
                deviceName: 'pH检测仪-01',
                deviceType: 'pH计',
                deviceModel: 'PH-2000',
                serialNumber: 'SN-PH-2023-001',
                status: 'online',
                lastActivity: '2023-06-15 14:30',
                location: '实验室A区',
                purchaseDate: '2023-01-10',
                warrantyExpiry: '2025-01-10',
                description: '高精度pH检测设备，测量范围0-14'
            },
            {
                deviceId: '2',
                deviceName: '溶解氧监测仪',
                deviceType: '溶解氧仪',
                deviceModel: 'DO-500',
                serialNumber: 'SN-DO-2023-002',
                status: 'online',
                lastActivity: '2023-06-15 14:25',
                location: '采样点1号',
                purchaseDate: '2023-02-15',
                warrantyExpiry: '2025-02-15',
                description: '实时监测溶解氧含量，带温度补偿'
            },
            {
                deviceId: '3',
                deviceName: '多参数水质仪',
                deviceType: '多参数水质检测仪',
                deviceModel: 'MP-3000',
                serialNumber: 'SN-MP-2023-003',
                status: 'warning',
                lastActivity: '2023-06-15 13:45',
                location: '野外监测站',
                purchaseDate: '2022-11-20',
                warrantyExpiry: '2024-11-20',
                description: '可同时测量pH、溶解氧、电导率等参数'
            },
            {
                deviceId: '4',
                deviceName: '浊度检测仪',
                deviceType: '浊度计',
                deviceModel: 'TB-100',
                serialNumber: 'SN-TB-2023-004',
                status: 'offline',
                lastActivity: '2023-06-14 09:15',
                location: '实验室B区',
                purchaseDate: '2023-03-05',
                warrantyExpiry: '2025-03-05',
                description: '高精度浊度测量，范围0-1000NTU'
            },
            {
                deviceId: '5',
                deviceName: '电导率监测仪',
                deviceType: '电导率仪',
                deviceModel: 'EC-400',
                serialNumber: 'SN-EC-2023-005',
                status: 'online',
                lastActivity: '2023-06-15 14:10',
                location: '采样点2号',
                purchaseDate: '2022-12-18',
                warrantyExpiry: '2024-12-18',
                description: '自动温度补偿，测量范围0-200mS/cm'
            },
            {
                deviceId: '6',
                deviceName: '便携式水质检测箱',
                deviceType: '多参数水质检测仪',
                deviceModel: 'PQC-200',
                serialNumber: 'SN-PQC-2023-006',
                status: 'online',
                lastActivity: '2023-06-15 11:30',
                location: '移动检测车',
                purchaseDate: '2023-04-22',
                warrantyExpiry: '2025-04-22',
                description: '便携式设计，适合野外快速检测'
            }
        ];

        // DOM元素
        const deviceTableBody = document.getElementById('deviceTableBody');
        const addDeviceBtn = document.getElementById('addDeviceBtn');
        const refreshDevicesBtn = document.getElementById('refreshDevicesBtn');
        const deviceModal = document.getElementById('deviceModal');
        const closeDeviceModal = document.getElementById('closeDeviceModal');
        const cancelDeviceBtn = document.getElementById('cancelDeviceBtn');
        const deviceForm = document.getElementById('deviceForm');
        const modalDeviceTitle = document.getElementById('modalDeviceTitle');
        const deviceSearch = document.getElementById('deviceSearch');

        // 渲染设备表格
        function renderDeviceTable(filteredDevices = devices) {
            deviceTableBody.innerHTML = '';

            filteredDevices.forEach(device => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                // 状态指示器
                let statusClass = 'status-offline';
                let statusText = '离线';
                if (device.status === 'online') {
                    statusClass = 'status-online';
                    statusText = '在线';
                } else if (device.status === 'warning') {
                    statusClass = 'status-warning';
                    statusText = '警告';
                }

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-500">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${device.deviceName}</div>
                                <div class="text-sm text-gray-500">${device.serialNumber}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${device.deviceType}</div>
                        <div class="text-sm text-gray-500">${device.deviceModel}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(device.status)}">
                            <span class="status-indicator ${statusClass}"></span>
                            ${statusText}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${device.lastActivity}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button class="text-blue-600 hover:text-blue-900 mr-3 edit-device-btn" data-id="${device.deviceId}">编辑</button>
                        <button class="text-red-600 hover:text-red-900 delete-device-btn" data-id="${device.deviceId}">删除</button>
                    </td>
                `;

                deviceTableBody.appendChild(row);
            });

            // 添加编辑和删除按钮事件
            document.querySelectorAll('.edit-device-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const id = btn.getAttribute('data-id');
                    editDevice(id);
                });
            });

            document.querySelectorAll('.delete-device-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const id = btn.getAttribute('data-id');
                    deleteDevice(id);
                });
            });

            // 添加行点击事件
            document.querySelectorAll('#deviceTableBody tr').forEach(row => {
                row.addEventListener('click', () => {
                    const id = row.querySelector('.edit-device-btn').getAttribute('data-id');
                    viewDeviceDetails(id);
                });
            });
        }

        // 根据状态获取徽章类
        function getStatusBadgeClass(status) {
            switch (status) {
                case 'online': return 'bg-green-100 text-green-800';
                case 'warning': return 'bg-amber-100 text-amber-800';
                case 'offline': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        // 查看设备详情
        function viewDeviceDetails(id) {
            const device = devices.find(d => d.deviceId === id);
            if (!device) return;

            alert(`查看设备详情:\n名称: ${device.deviceName}\n类型: ${device.deviceType}\n型号: ${device.deviceModel}\n序列号: ${device.serialNumber}\n位置: ${device.location}\n状态: ${device.status}\n最后活动: ${device.lastActivity}`);
        }

        // 编辑设备
        function editDevice(id) {
            const device = devices.find(d => d.deviceId === id);
            if (!device) return;

            modalDeviceTitle.textContent = '编辑设备信息';
            document.getElementById('deviceId').value = device.deviceId;
            document.getElementById('deviceName').value = device.deviceName;
            document.getElementById('deviceType').value = device.deviceType;
            document.getElementById('deviceModel').value = device.deviceModel;
            document.getElementById('serialNumber').value = device.serialNumber;
            document.getElementById('deviceLocation').value = device.location;
            document.getElementById('purchaseDate').value = device.purchaseDate;
            document.getElementById('warrantyExpiry').value = device.warrantyExpiry;
            document.getElementById('deviceDescription').value = device.description;

            // 设置设备状态
            let statusValue = 'active';
            if (device.status === 'warning') statusValue = 'maintenance';
            if (device.status === 'offline') statusValue = 'inactive';
            document.querySelector(`input[name="deviceStatus"][value="${statusValue}"]`).checked = true;

            openDeviceModal();
        }

        // 删除设备
        function deleteDevice(id) {
            if (confirm('确定要删除这个设备吗？此操作不可恢复。')) {
                const index = devices.findIndex(d => d.deviceId === id);
                if (index !== -1) {
                    devices.splice(index, 1);
                    renderDeviceTable();
                    alert('设备已删除');
                }
            }
        }

        // 打开设备模态框
        function openDeviceModal() {
            deviceModal.classList.remove('hidden');
        }

        // 关闭设备模态框
        function closeDeviceModalFunc() {
            deviceModal.classList.add('hidden');
            deviceForm.reset();
        }

        // 初始化
        function init() {
            renderDeviceTable();

            // 事件监听
            addDeviceBtn.addEventListener('click', () => {
                modalDeviceTitle.textContent = '添加新设备';
                document.getElementById('deviceId').value = '';
                deviceForm.reset();
                openDeviceModal();
            });

            refreshDevicesBtn.addEventListener('click', () => {
                // 模拟刷新数据
                alert('设备列表已刷新');
            });

            closeDeviceModal.addEventListener('click', closeDeviceModalFunc);
            cancelDeviceBtn.addEventListener('click', closeDeviceModalFunc);

            // 表单提交
            deviceForm.addEventListener('submit', (e) => {
                e.preventDefault();

                const formData = new FormData(deviceForm);
                const data = {
                    deviceId: formData.get('deviceId'),
                    deviceName: formData.get('deviceName'),
                    deviceType: formData.get('deviceType'),
                    deviceModel: formData.get('deviceModel'),
                    serialNumber: formData.get('serialNumber'),
                    location: formData.get('deviceLocation'),
                    purchaseDate: formData.get('purchaseDate'),
                    warrantyExpiry: formData.get('warrantyExpiry'),
                    description: formData.get('deviceDescription'),
                    lastActivity: new Date().toISOString().slice(0, 16).replace('T', ' '),
                    status: 'online' // 默认设置为在线
                };

                // 根据表单状态设置设备状态
                const deviceStatus = formData.get('deviceStatus');
                if (deviceStatus === 'maintenance') data.status = 'warning';
                if (deviceStatus === 'inactive') data.status = 'offline';

                if (data.deviceId) {
                    // 更新现有设备
                    const index = devices.findIndex(d => d.deviceId === data.deviceId);
                    if (index !== -1) {
                        devices[index] = { ...devices[index], ...data };
                    }
                } else {
                    // 新增设备
                    data.deviceId = (devices.length + 1).toString();
                    devices.push(data);
                }

                renderDeviceTable();
                closeDeviceModalFunc();
                alert('设备信息已保存');
            });

            // 搜索功能
            deviceSearch.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                if (!searchTerm) {
                    renderDeviceTable();
                    return;
                }

                const filtered = devices.filter(d =>
                    d.deviceName.toLowerCase().includes(searchTerm) ||
                    d.deviceType.toLowerCase().includes(searchTerm) ||
                    d.serialNumber.toLowerCase().includes(searchTerm)
                );
                renderDeviceTable(filtered);
            });
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>



<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-xl shadow-sm p-5">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 text-sm">总设备数</p>
                <h3 class="text-2xl font-bold text-gray-800 mt-1">24</h3>
            </div>
            <div class="p-3 bg-blue-100 rounded-lg text-blue-600">
                <i class="fas fa-microscope text-xl"></i>
            </div>
        </div>
        <div class="mt-4 pt-3 border-t border-gray-100">
            <p class="text-sm text-gray-500 flex items-center">
                <span class="status-indicator status-online"></span>
                <span>在线: <span class="font-medium text-gray-700">18</span></span>
            </p>
        </div>
    </div>

    <!-- 其他概览卡片... -->


    <div class="flex-1 bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="p-5 border-b border-gray-100">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">设备列表</h3>
                <div class="relative">
                    <input type="text" id="deviceSearch" placeholder="搜索设备..."
                        class="w-48 pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
            </div>
        </div>
        <div class="overflow-y-auto" style="max-height: 500px;">
            <table class="min-w-full divide-y divide-gray-200">
                <!-- 表格内容... -->
            </table>
        </div>
    </div>

    <div class="flex-1 bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="p-5 border-b border-gray-100">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">设备列表</h3>
                <div class="relative">
                    <input type="text" id="deviceSearch" placeholder="搜索设备..."
                        class="w-48 pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
            </div>
        </div>
        <div class="overflow-y-auto" style="max-height: 500px;">
            <table class="min-w-full divide-y divide-gray-200">
                <!-- 表格内容... -->
            </table>
        </div>
    </div>



</div>

<div class="lg:w-96 bg-white rounded-xl shadow-sm overflow-hidden">
    <div class="p-5 border-b border-gray-100">
        <h3 class="text-lg font-semibold text-gray-800">设备状态监控</h3>
    </div>
    <div class="p-5">
        <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">设备连接状态</h4>
            <div class="h-40 bg-gray-50 rounded-lg flex items-center justify-center">
                <!-- 连接状态指示... -->
            </div>
        </div>

        <!-- 其他监控区域... -->
    </div>
</div>



</html>