<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频简介制作软件 - VideoIntro Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #1e1e2e 0%, #2d1b69 50%, #11998e 100%);
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="0.3" fill="rgba(255,255,255,0.01)"/><circle cx="50" cy="10" r="0.4" fill="rgba(255,255,255,0.015)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .app-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
            z-index: 2;
        }

        .header {
            height: 70px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.12);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            color: white;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                    rgba(255, 255, 255, 0.1) 0%,
                    rgba(255, 255, 255, 0.05) 50%,
                    rgba(255, 255, 255, 0.1) 100%);
            pointer-events: none;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 22px;
            font-weight: 700;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
        }

        .logo i {
            margin-right: 12px;
            font-size: 28px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
        }

        .header-controls {
            display: flex;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            text-decoration: none;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
            color: white;
            box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 40px rgba(255, 107, 107, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 320px;
            background: rgba(255, 255, 255, 0.06);
            backdrop-filter: blur(25px) saturate(180%);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            overflow-y: auto;
            position: relative;
            color: white;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg,
                    rgba(255, 255, 255, 0.08) 0%,
                    rgba(255, 255, 255, 0.02) 50%,
                    rgba(255, 255, 255, 0.08) 100%);
            pointer-events: none;
        }

        .sidebar>* {
            position: relative;
            z-index: 1;
        }

        .canvas-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .canvas-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30px;
            position: relative;
            background:
                radial-gradient(circle at 30% 70%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(238, 90, 36, 0.1) 0%, transparent 50%);
        }

        #mainCanvas {
            border-radius: 20px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            background: #000;
            position: relative;
            overflow: hidden;
        }

        #mainCanvas::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.05) 0%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.02) 100%);
            pointer-events: none;
        }

        .timeline {
            height: 220px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.15);
            padding: 20px;
            overflow-x: auto;
            position: relative;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg,
                    rgba(255, 255, 255, 0.05) 0%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.02) 100%);
            pointer-events: none;
        }

        .section-title {
            color: white;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            letter-spacing: -0.3px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 2px;
        }

        .template-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .template-item {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 16px;
            padding: 20px 15px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .template-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 36, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .template-item:hover::before {
            opacity: 1;
        }

        .template-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 107, 107, 0.3);
        }

        .template-item i {
            font-size: 28px;
            margin-bottom: 12px;
            display: block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }

        .template-item span {
            font-size: 13px;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 25px;
            backdrop-filter: blur(15px);
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            color: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            letter-spacing: -0.2px;
        }

        .control-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-input:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.15);
            background: rgba(255, 255, 255, 0.12);
        }

        .control-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 5px 0;
        }

        .slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
            appearance: none;
            position: relative;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
            transition: all 0.2s ease;
        }

        .slider::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            cursor: pointer;
            border: none;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .slider-value {
            min-width: 40px;
            text-align: center;
            font-size: 13px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .color-picker {
            width: 100%;
            height: 50px;
            border: 2px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: transparent;
        }

        .color-picker:hover {
            border-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.02);
        }

        .timeline-controls {
            position: relative;
            z-index: 1;
        }

        .timeline-track {
            height: 70px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .timeline-clip {
            height: 100%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
            border-radius: 10px;
            position: absolute;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 13px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
        }

        .timeline-clip::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                    rgba(255, 255, 255, 0.2) 0%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.1) 100%);
        }

        .timeline-clip:hover {
            transform: scale(1.02);
            box-shadow: 0 6px 25px rgba(255, 107, 107, 0.4);
        }

        .playback-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .play-btn {
            width: 55px;
            height: 55px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            font-size: 22px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .play-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .play-btn:hover::before {
            left: 100%;
        }

        .play-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
        }

        .time-display {
            color: rgba(255, 255, 255, 0.95);
            font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 16px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 18px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            letter-spacing: 0.5px;
        }

        .floating-panel {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(0, 0, 0, 0.85);
            backdrop-filter: blur(25px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 30px;
            color: white;
            min-width: 300px;
            display: none;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
        }

        .floating-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.1) 0%,
                    rgba(255, 255, 255, 0.05) 50%,
                    rgba(255, 255, 255, 0.1) 100%);
            pointer-events: none;
        }

        .floating-panel>* {
            position: relative;
            z-index: 1;
        }

        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 25px;
            color: white;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .panel-title i {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .export-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .quality-option {
            padding: 15px 18px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 500;
        }

        .quality-option:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 107, 107, 0.3);
        }

        .quality-option.selected {
            background: rgba(255, 107, 107, 0.2);
            border-color: rgba(255, 107, 107, 0.5);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 20px;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                    rgba(255, 255, 255, 0.3) 0%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.3) 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }
    </style>
</head>

<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-video"></i>
                VideoIntro Pro
            </div>
            <div class="header-controls">
                <button class="btn btn-secondary">
                    <i class="fas fa-folder-open"></i>
                    打开项目
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-save"></i>
                    保存
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    导出视频
                </button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧工具栏 -->
            <aside class="sidebar">
                <!-- 模板选择 -->
                <div class="section-title">
                    <i class="fas fa-magic"></i>
                    视频模板
                </div>
                <div class="template-grid">
                    <div class="template-item" data-template="intro">
                        <i class="fas fa-play-circle"></i>
                        <div>开场介绍</div>
                    </div>
                    <div class="template-item" data-template="logo">
                        <i class="fas fa-star"></i>
                        <div>Logo展示</div>
                    </div>
                    <div class="template-item" data-template="text">
                        <i class="fas fa-font"></i>
                        <div>文字动画</div>
                    </div>
                    <div class="template-item" data-template="transition">
                        <i class="fas fa-exchange-alt"></i>
                        <div>转场效果</div>
                    </div>
                </div>

                <!-- 控制面板 -->
                <div class="controls-panel">
                    <div class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        属性控制
                    </div>

                    <div class="control-group">
                        <label class="control-label">文字内容</label>
                        <input type="text" id="textInput" placeholder="输入文字内容..."
                            style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.1); color: white;">
                    </div>

                    <div class="control-group">
                        <label class="control-label">字体大小: <span id="fontSizeValue">48</span>px</label>
                        <input type="range" class="slider" id="fontSizeSlider" min="20" max="100" value="48">
                    </div>

                    <div class="control-group">
                        <label class="control-label">文字颜色</label>
                        <input type="color" class="color-picker" id="textColor" value="#ffffff">
                    </div>

                    <div class="control-group">
                        <label class="control-label">背景颜色</label>
                        <input type="color" class="color-picker" id="bgColor" value="#000000">
                    </div>

                    <div class="control-group">
                        <label class="control-label">动画速度: <span id="speedValue">1.0</span>x</label>
                        <input type="range" class="slider" id="speedSlider" min="0.5" max="3" step="0.1" value="1.0">
                    </div>
                </div>

                <!-- 图层管理 -->
                <div class="controls-panel">
                    <div class="section-title">
                        <i class="fas fa-layers"></i>
                        图层管理
                    </div>
                    <div id="layersList">
                        <div class="layer-item"
                            style="padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px; margin-bottom: 5px; color: white; cursor: pointer;">
                            <i class="fas fa-eye"></i> 背景层
                        </div>
                        <div class="layer-item"
                            style="padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px; margin-bottom: 5px; color: white; cursor: pointer;">
                            <i class="fas fa-font"></i> 文字层
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 中央Canvas区域 -->
            <div class="canvas-area">
                <div class="canvas-container">
                    <canvas id="mainCanvas" width="800" height="450"></canvas>

                    <!-- 浮动导出面板 -->
                    <div class="floating-panel" id="exportPanel" style="display: none;">
                        <div class="section-title">
                            <i class="fas fa-download"></i>
                            导出设置
                        </div>
                        <div class="export-options">
                            <div class="quality-option" data-quality="720p">
                                <strong>720p HD</strong><br>
                                <small>1280x720, 适合网络分享</small>
                            </div>
                            <div class="quality-option" data-quality="1080p">
                                <strong>1080p Full HD</strong><br>
                                <small>1920x1080, 高清质量</small>
                            </div>
                            <div class="quality-option" data-quality="4k">
                                <strong>4K Ultra HD</strong><br>
                                <small>3840x2160, 超高清</small>
                            </div>
                        </div>
                        <button class="btn btn-primary" style="width: 100%; margin-top: 15px;" onclick="startExport()">
                            <i class="fas fa-play"></i>
                            开始导出
                        </button>
                        <div class="progress-bar" id="exportProgress" style="display: none;">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                    </div>
                </div>

                <!-- 时间轴区域 -->
                <div class="timeline">
                    <div class="playback-controls">
                        <button class="btn btn-secondary">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button class="play-btn" id="playBtn">
                            <i class="fas fa-play" id="playIcon"></i>
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-step-forward"></i>
                        </button>
                        <div class="time-display">
                            <span id="currentTime">00:00</span> / <span id="totalTime">00:05</span>
                        </div>
                    </div>

                    <!-- 时间轴轨道 -->
                    <div class="timeline-track">
                        <div class="timeline-clip" style="left: 0%; width: 40%;">
                            背景动画
                        </div>
                        <div class="timeline-clip"
                            style="left: 20%; width: 60%; background: linear-gradient(45deg, #4ecdc4, #44a08d);">
                            文字动画
                        </div>
                    </div>

                    <div class="timeline-track">
                        <div class="timeline-clip"
                            style="left: 0%; width: 100%; background: linear-gradient(45deg, #f093fb, #f5576c);">
                            音频轨道
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Canvas和动画相关变量
        const canvas = document.getElementById('mainCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let isPlaying = false;
        let currentFrame = 0;
        let totalFrames = 150; // 5秒 * 30fps
        let animationSpeed = 1.0;

        // 当前场景数据
        let sceneData = {
            text: 'VideoIntro Pro',
            fontSize: 48,
            textColor: '#ffffff',
            backgroundColor: '#000000',
            template: 'intro'
        };

        // 初始化Canvas
        function initCanvas() {
            // 设置Canvas高DPI支持
            const dpr = window.devicePixelRatio || 1;
            const rect = canvas.getBoundingClientRect();

            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;
            ctx.scale(dpr, dpr);

            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';

            drawFrame();
        }

        // 绘制当前帧
        function drawFrame() {
            const progress = currentFrame / totalFrames;

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景
            drawBackground(progress);

            // 根据模板绘制内容
            switch (sceneData.template) {
                case 'intro':
                    drawIntroAnimation(progress);
                    break;
                case 'logo':
                    drawLogoAnimation(progress);
                    break;
                case 'text':
                    drawTextAnimation(progress);
                    break;
                case 'transition':
                    drawTransitionAnimation(progress);
                    break;
            }
        }

        // 绘制背景
        function drawBackground(progress) {
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);

            if (sceneData.template === 'intro') {
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
            } else {
                gradient.addColorStop(0, sceneData.backgroundColor);
                gradient.addColorStop(1, adjustBrightness(sceneData.backgroundColor, -20));
            }

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 添加动态粒子效果
            drawParticles(progress);
        }

        // 绘制粒子效果
        function drawParticles(progress) {
            const particleCount = 50;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';

            for (let i = 0; i < particleCount; i++) {
                const x = (Math.sin(progress * Math.PI * 2 + i) * 100 + canvas.width / 2) % canvas.width;
                const y = (Math.cos(progress * Math.PI * 2 + i * 0.5) * 50 + canvas.height / 2) % canvas.height;
                const size = Math.sin(progress * Math.PI * 4 + i) * 2 + 3;

                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        // 开场介绍动画
        function drawIntroAnimation(progress) {
            ctx.save();

            // 文字淡入效果
            const textAlpha = Math.min(progress * 2, 1);
            const scale = 0.5 + progress * 0.5;

            ctx.globalAlpha = textAlpha;
            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.scale(scale, scale);

            // 绘制主标题
            ctx.fillStyle = sceneData.textColor;
            ctx.font = `bold ${sceneData.fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 添加文字阴影
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;

            ctx.fillText(sceneData.text, 0, -20);

            // 绘制副标题
            ctx.font = `${sceneData.fontSize * 0.4}px Arial`;
            ctx.fillText('专业视频制作工具', 0, 30);

            ctx.restore();
        }

        // Logo展示动画
        function drawLogoAnimation(progress) {
            ctx.save();

            const rotation = progress * Math.PI * 2;
            const scale = Math.sin(progress * Math.PI) * 0.3 + 0.7;

            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.rotate(rotation);
            ctx.scale(scale, scale);

            // 绘制Logo图标
            ctx.fillStyle = '#ffd700';
            ctx.font = `${sceneData.fontSize * 2}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('★', 0, 0);

            ctx.restore();
        }

        // 文字动画
        function drawTextAnimation(progress) {
            ctx.save();

            // 打字机效果
            const textLength = sceneData.text.length;
            const visibleChars = Math.floor(progress * textLength);
            const displayText = sceneData.text.substring(0, visibleChars);

            ctx.fillStyle = sceneData.textColor;
            ctx.font = `bold ${sceneData.fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 添加发光效果
            ctx.shadowColor = sceneData.textColor;
            ctx.shadowBlur = 20;

            ctx.fillText(displayText, canvas.width / 2, canvas.height / 2);

            // 光标效果
            if (progress < 0.9) {
                const cursorX = ctx.measureText(displayText).width / 2 + canvas.width / 2;
                ctx.fillRect(cursorX + 5, canvas.height / 2 - sceneData.fontSize / 2, 3, sceneData.fontSize);
            }

            ctx.restore();
        }

        // 转场动画
        function drawTransitionAnimation(progress) {
            ctx.save();

            // 创建遮罩效果
            const maskRadius = progress * Math.sqrt(canvas.width * canvas.width + canvas.height * canvas.height);

            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height / 2, maskRadius, 0, Math.PI * 2);
            ctx.clip();

            // 绘制新场景
            ctx.fillStyle = sceneData.backgroundColor;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = sceneData.textColor;
            ctx.font = `bold ${sceneData.fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(sceneData.text, canvas.width / 2, canvas.height / 2);

            ctx.restore();
        }

        // 工具函数：调整颜色亮度
        function adjustBrightness(color, amount) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * amount);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        // 动画循环
        function animate() {
            if (isPlaying) {
                currentFrame += animationSpeed;
                if (currentFrame >= totalFrames) {
                    currentFrame = 0; // 循环播放
                }

                drawFrame();
                updateTimeDisplay();
                animationId = requestAnimationFrame(animate);
            }
        }

        // 播放/暂停控制
        function togglePlayback() {
            isPlaying = !isPlaying;
            const playIcon = document.getElementById('playIcon');

            if (isPlaying) {
                playIcon.className = 'fas fa-pause';
                animate();
            } else {
                playIcon.className = 'fas fa-play';
                cancelAnimationFrame(animationId);
            }
        }

        // 更新时间显示
        function updateTimeDisplay() {
            const currentSeconds = Math.floor((currentFrame / totalFrames) * 5);
            const currentTime = `00:0${currentSeconds}`;
            document.getElementById('currentTime').textContent = currentTime;
        }

        // 导出功能
        function startExport() {
            const exportPanel = document.getElementById('exportPanel');
            const progressBar = document.getElementById('exportProgress');
            const progressFill = document.getElementById('progressFill');

            progressBar.style.display = 'block';

            // 模拟导出进度
            let progress = 0;
            const exportInterval = setInterval(() => {
                progress += 2;
                progressFill.style.width = progress + '%';

                if (progress >= 100) {
                    clearInterval(exportInterval);
                    setTimeout(() => {
                        alert('视频导出完成！');
                        progressBar.style.display = 'none';
                        progressFill.style.width = '0%';
                        exportPanel.style.display = 'none';
                    }, 500);
                }
            }, 50);
        }

        // 事件监听器设置
        function setupEventListeners() {
            // 播放按钮
            document.getElementById('playBtn').addEventListener('click', togglePlayback);

            // 模板选择
            document.querySelectorAll('.template-item').forEach(item => {
                item.addEventListener('click', () => {
                    const template = item.getAttribute('data-template');
                    sceneData.template = template;
                    drawFrame();

                    // 更新选中状态
                    document.querySelectorAll('.template-item').forEach(t => t.style.background = 'rgba(255, 255, 255, 0.1)');
                    item.style.background = 'rgba(255, 255, 255, 0.3)';
                });
            });

            // 文字输入
            document.getElementById('textInput').addEventListener('input', (e) => {
                sceneData.text = e.target.value || 'VideoIntro Pro';
                drawFrame();
            });

            // 字体大小滑块
            document.getElementById('fontSizeSlider').addEventListener('input', (e) => {
                sceneData.fontSize = parseInt(e.target.value);
                document.getElementById('fontSizeValue').textContent = e.target.value;
                drawFrame();
            });

            // 文字颜色
            document.getElementById('textColor').addEventListener('change', (e) => {
                sceneData.textColor = e.target.value;
                drawFrame();
            });

            // 背景颜色
            document.getElementById('bgColor').addEventListener('change', (e) => {
                sceneData.backgroundColor = e.target.value;
                drawFrame();
            });

            // 动画速度
            document.getElementById('speedSlider').addEventListener('input', (e) => {
                animationSpeed = parseFloat(e.target.value);
                document.getElementById('speedValue').textContent = e.target.value;
            });

            // 导出按钮
            document.querySelector('.btn-primary').addEventListener('click', () => {
                document.getElementById('exportPanel').style.display = 'block';
            });

            // 质量选项
            document.querySelectorAll('.quality-option').forEach(option => {
                option.addEventListener('click', () => {
                    document.querySelectorAll('.quality-option').forEach(o => o.style.background = 'rgba(255, 255, 255, 0.1)');
                    option.style.background = 'rgba(255, 255, 255, 0.3)';
                });
            });

            // 图层管理
            document.querySelectorAll('.layer-item').forEach(layer => {
                layer.addEventListener('click', () => {
                    document.querySelectorAll('.layer-item').forEach(l => l.style.background = 'rgba(255, 255, 255, 0.1)');
                    layer.style.background = 'rgba(255, 255, 255, 0.3)';
                });
            });
        }

        // 初始化应用
        function initApp() {
            initCanvas();
            setupEventListeners();

            // 设置默认选中的模板
            document.querySelector('[data-template="intro"]').style.background = 'rgba(255, 255, 255, 0.3)';

            // 窗口大小改变时重新初始化Canvas
            window.addEventListener('resize', () => {
                setTimeout(initCanvas, 100);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>

</html>